'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { usePathname } from 'next/navigation'

interface LoadingContextType {
  isLoading: boolean
  setLoading: (loading: boolean) => void
  loadingMessage: string
  setLoadingMessage: (message: string) => void
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined)

export function useLoading() {
  const context = useContext(LoadingContext)
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider')
  }
  return context
}

interface LoadingProviderProps {
  children: ReactNode
}

export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('Loading...')
  const pathname = usePathname()

  const setLoading = (loading: boolean) => {
    setIsLoading(loading)
  }

  // Auto-hide loading on route change
  useEffect(() => {
    setIsLoading(false)
  }, [pathname])

  return (
    <LoadingContext.Provider value={{
      isLoading,
      setLoading,
      loadingMessage,
      setLoadingMessage
    }}>
      {children}
      {isLoading && <LoadingOverlay message={loadingMessage} />}
    </LoadingContext.Provider>
  )
}

interface LoadingOverlayProps {
  message: string
}

function LoadingOverlay({ message }: LoadingOverlayProps) {
  return (
    <div className="fixed inset-0 z-[9999] bg-background/80 backdrop-blur-sm flex items-center justify-center">
      <div className="bg-card rounded-xl shadow-lg border border-border p-8 max-w-sm w-full mx-4">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 mx-auto">
            <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Please wait</h3>
            <p className="text-muted-foreground text-sm">{message}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook for navigation with loading
export function useNavigationLoading() {
  const { setLoading, setLoadingMessage } = useLoading()
  const router = useRouter()

  const navigateWithLoading = (href: string, message: string = 'Loading page...') => {
    setLoadingMessage(message)
    setLoading(true)
    router.push(href)
  }

  const refreshWithLoading = (message: string = 'Refreshing...') => {
    setLoadingMessage(message)
    setLoading(true)
    router.refresh()
  }

  return {
    navigateWithLoading,
    refreshWithLoading,
    setLoading,
    setLoadingMessage
  }
}
