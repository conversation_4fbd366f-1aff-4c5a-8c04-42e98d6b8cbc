'use client'

import { useEffect, useState } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

export function PageLoadingIndicator() {
  const [isLoading, setIsLoading] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handleStart = () => setIsLoading(true)
    const handleComplete = () => setIsLoading(false)

    // Listen for route changes
    handleComplete() // Complete loading when route changes

    return () => {
      handleComplete()
    }
  }, [pathname, searchParams])

  if (!isLoading) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div className="h-1 bg-primary/20">
        <div className="h-full bg-primary animate-pulse" style={{
          animation: 'loading-bar 2s ease-in-out infinite'
        }} />
      </div>
    </div>
  )
}

// Skeleton components for different page types
export function AuthPageSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header Skeleton */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="h-6 w-20 bg-muted rounded animate-pulse"></div>
            <div className="h-10 w-10 bg-muted rounded animate-pulse"></div>
          </div>
        </div>
      </header>

      {/* Main Content Skeleton */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-muted rounded-full animate-pulse"></div>
            <div className="h-8 w-48 mx-auto bg-muted rounded animate-pulse"></div>
            <div className="h-4 w-64 mx-auto bg-muted rounded animate-pulse"></div>
          </div>
          
          <div className="bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8">
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
                <div className="h-12 w-full bg-muted rounded-lg animate-pulse"></div>
              </div>
              <div className="space-y-2">
                <div className="h-4 w-16 bg-muted rounded animate-pulse"></div>
                <div className="h-12 w-full bg-muted rounded-lg animate-pulse"></div>
              </div>
              <div className="h-12 w-full bg-muted rounded-lg animate-pulse"></div>
              <div className="h-4 w-32 mx-auto bg-muted rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export function PostPageSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header Skeleton */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="h-6 w-20 bg-muted rounded animate-pulse"></div>
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 bg-muted rounded animate-pulse"></div>
              <div className="h-10 w-20 bg-muted rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content Skeleton */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Title and meta */}
          <div className="space-y-4">
            <div className="h-10 w-3/4 bg-muted rounded animate-pulse"></div>
            <div className="flex items-center gap-4">
              <div className="h-4 w-32 bg-muted rounded animate-pulse"></div>
              <div className="h-4 w-24 bg-muted rounded animate-pulse"></div>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 w-full bg-muted rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-muted rounded animate-pulse"></div>
                {i % 3 === 0 && <div className="h-4 w-2/3 bg-muted rounded animate-pulse"></div>}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Loading states for forms
export function FormLoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="h-4 w-20 bg-muted rounded animate-pulse"></div>
        <div className="h-12 w-full bg-muted rounded-lg animate-pulse"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 w-16 bg-muted rounded animate-pulse"></div>
        <div className="h-32 w-full bg-muted rounded-lg animate-pulse"></div>
      </div>
      <div className="h-12 w-full bg-muted rounded-lg animate-pulse"></div>
    </div>
  )
}

// Inline loading spinner
export function InlineSpinner({ size = 'sm', className = '' }: { size?: 'sm' | 'md' | 'lg', className?: string }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  return (
    <div className={`${sizeClasses[size]} border-2 border-current border-t-transparent rounded-full animate-spin ${className}`} />
  )
}
