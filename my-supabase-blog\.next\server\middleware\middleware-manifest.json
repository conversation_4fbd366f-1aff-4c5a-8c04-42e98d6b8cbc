{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "InWAx9TKRw0SwjwgHaN9o9iJ2f59fSsKSswH0rIg4hk=", "__NEXT_PREVIEW_MODE_ID": "98bcf47cd3f11acf524b52682bb438c5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f6673a8a2a3865baeda6dc1a8eef6a9db241d9dde9cbaaa53e9d78061b6c63cc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e1f53d196b5cbfafbba1bb16efc3f099219bee2b31f91c8155cf02354d7c6312"}}}, "instrumentation": null, "functions": {}}