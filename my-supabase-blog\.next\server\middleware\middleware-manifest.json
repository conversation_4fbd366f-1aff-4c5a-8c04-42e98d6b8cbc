{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "InWAx9TKRw0SwjwgHaN9o9iJ2f59fSsKSswH0rIg4hk=", "__NEXT_PREVIEW_MODE_ID": "3d5daa34da4acce7557f8e38432d1ab6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b8ab49d4ffb383bce9db883f3dd7d4294c9abb850730db91594cb618bb7cf574", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "39ede885d640be8608bb06dde1d28a7fbbab227ab799e9af7d389bddb05e5293"}}}, "instrumentation": null, "functions": {}}