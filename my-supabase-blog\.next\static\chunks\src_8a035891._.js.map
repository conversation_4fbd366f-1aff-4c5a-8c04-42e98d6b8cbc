{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\n\nexport function ThemeToggle() {\n  const { theme, setTheme } = useTheme()\n  const [mounted, setMounted] = React.useState(false)\n\n  React.useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <button className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n        <span className=\"sr-only\">切換主題</span>\n      </button>\n    )\n  }\n\n  return (\n    <button\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\n      className=\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\"\n    >\n      {theme === 'light' ? (\n        <Moon className=\"h-[1.2rem] w-[1.2rem]\" />\n      ) : (\n        <Sun className=\"h-[1.2rem] w-[1.2rem]\" />\n      )}\n      <span className=\"sr-only\">切換主題</span>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAO,WAAU;;8BAChB,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC;QACC,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;YAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAEhB,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BAEjB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GA9BgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/client-theme-toggle.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeToggle } from './theme-toggle'\r\n\r\nexport function ClientThemeToggle() {\r\n  return <ThemeToggle />\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,qBAAO,6LAAC,wIAAA,CAAA,cAAW;;;;;AACrB;KAFgB", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookieOptions: {\n        domain: new URL(siteUrl).hostname,\n        sameSite: 'lax',\n        secure: true\n      },\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n      }\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAGkB;AAHlB;AAAA;;AAEO,SAAS;IACd,MAAM,UAAU,0EAAoC;IACpD,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,sUAGvB;QACE,eAAe;YACb,QAAQ,IAAI,IAAI,SAAS,QAAQ;YACjC,UAAU;YACV,QAAQ;QACV;QACA,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;QACtB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/auth-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport Link from 'next/link'\nimport { useNavigationLoading } from '@/components/loading-provider'\n\ninterface AuthFormProps {\n  mode: 'signin' | 'signup'\n  onModeChange: (mode: 'signin' | 'signup') => void\n  onSuccess?: () => void\n  onError?: (error: string) => void\n}\n\nexport function AuthForm({ mode, onModeChange, onSuccess, onError }: AuthFormProps) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const [errors, setErrors] = useState<{email?: string; password?: string}>({})\n  const [showPassword, setShowPassword] = useState(false)\n  \n  const supabase = createClient()\n  const { navigateWithLoading } = useNavigationLoading()\n\n  const validateForm = () => {\n    const newErrors: {email?: string; password?: string} = {}\n    \n    // Email validation\n    if (!email) {\n      newErrors.email = 'Email is required'\n    } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\n      newErrors.email = 'Please enter a valid email address'\n    }\n    \n    // Password validation\n    if (!password) {\n      newErrors.password = 'Password is required'\n    } else if (mode === 'signup' && password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters long'\n    }\n    \n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setMessage('')\n    setErrors({})\n\n    // Validate form\n    if (!validateForm()) {\n      setLoading(false)\n      return\n    }\n\n    try {\n      if (mode === 'signup') {\n        const { error } = await supabase.auth.signUp({\n          email,\n          password,\n        })\n        if (error) throw error\n        setMessage('Registration successful! Please check your email to verify your account.')\n        onSuccess?.()\n      } else {\n        const { error } = await supabase.auth.signInWithPassword({\n          email,\n          password,\n        })\n        if (error) throw error\n        navigateWithLoading('/', 'Signing you in...')\n        onSuccess?.()\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'An error occurred'\n      setMessage(errorMessage)\n      onError?.(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const clearFieldError = (field: 'email' | 'password') => {\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }))\n    }\n  }\n\n  return (\n    <form className=\"space-y-6\" onSubmit={handleSubmit}>\n      <div className=\"space-y-5\">\n        {/* Email Field */}\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\n            Email address\n          </label>\n          <input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground ${\n              errors.email \n                ? 'border-red-500 bg-red-50 dark:bg-red-900/10 text-foreground' \n                : 'border-input bg-background text-foreground'\n            }`}\n            placeholder=\"Enter your email address\"\n            value={email}\n            onChange={(e) => {\n              setEmail(e.target.value)\n              clearFieldError('email')\n            }}\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.email}</p>\n          )}\n        </div>\n\n        {/* Password Field */}\n        <div>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-foreground mb-2\">\n            Password\n          </label>\n          <div className=\"relative\">\n            <input\n              id=\"password\"\n              name=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              autoComplete={mode === 'signup' ? 'new-password' : 'current-password'}\n              required\n              className={`w-full px-4 py-3 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground ${\n                errors.password \n                  ? 'border-red-500 bg-red-50 dark:bg-red-900/10 text-foreground' \n                  : 'border-input bg-background text-foreground'\n              }`}\n              placeholder={mode === 'signup' ? 'Create a password (min. 6 characters)' : 'Enter your password'}\n              value={password}\n              onChange={(e) => {\n                setPassword(e.target.value)\n                clearFieldError('password')\n              }}\n            />\n            <button\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              {showPassword ? (\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                </svg>\n              ) : (\n                <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                </svg>\n              )}\n            </button>\n          </div>\n          {errors.password && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.password}</p>\n          )}\n          {mode === 'signup' && !errors.password && (\n            <p className=\"mt-1 text-xs text-muted-foreground\">\n              Password must be at least 6 characters long\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Error/Success Message */}\n      {message && (\n        <div className={`p-4 rounded-lg text-sm ${\n          message.includes('successful')\n            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800'\n            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800'\n        }`}>\n          {message}\n        </div>\n      )}\n\n      {/* Submit Button */}\n      <div>\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md\"\n        >\n          {loading ? (\n            <div className=\"flex items-center justify-center\">\n              <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"></div>\n              {mode === 'signup' ? 'Creating account...' : 'Signing in...'}\n            </div>\n          ) : (\n            mode === 'signup' ? 'Create Account' : 'Sign In'\n          )}\n        </button>\n      </div>\n\n      {/* Mode Toggle and Forgot Password */}\n      <div className=\"text-center space-y-3\">\n        <button\n          type=\"button\"\n          onClick={() => onModeChange(mode === 'signup' ? 'signin' : 'signup')}\n          className=\"text-primary hover:text-primary/80 text-sm transition-colors font-medium\"\n        >\n          {mode === 'signup' ? 'Already have an account? Sign in' : \"Don't have an account? Sign up\"}\n        </button>\n        \n        {mode === 'signin' && (\n          <div>\n            <Link\n              href=\"/forgot-password\"\n              className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n            >\n              Forgot your password?\n            </Link>\n          </div>\n        )}\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcO,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAiB;;IAChF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,uBAAoB,AAAD;IAEnD,MAAM,eAAe;QACnB,MAAM,YAAiD,CAAC;QAExD,mBAAmB;QACnB,IAAI,CAAC,OAAO;YACV,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,QAAQ;YACtC,UAAU,KAAK,GAAG;QACpB;QAEA,sBAAsB;QACtB,IAAI,CAAC,UAAU;YACb,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,YAAY,SAAS,MAAM,GAAG,GAAG;YACnD,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;QACX,UAAU,CAAC;QAEX,gBAAgB;QAChB,IAAI,CAAC,gBAAgB;YACnB,WAAW;YACX;QACF;QAEA,IAAI;YACF,IAAI,SAAS,UAAU;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,WAAW;gBACX;YACF,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,oBAAoB,KAAK;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,WAAW;YACX,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACpD;IACF;IAEA,qBACE,6LAAC;QAAK,WAAU;QAAY,UAAU;;0BACpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAAiD;;;;;;0CAGlF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,WAAW,CAAC,0KAA0K,EACpL,OAAO,KAAK,GACR,gEACA,8CACJ;gCACF,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC;oCACT,SAAS,EAAE,MAAM,CAAC,KAAK;oCACvB,gBAAgB;gCAClB;;;;;;4BAED,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA+C,OAAO,KAAK;;;;;;;;;;;;kCAK5E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAiD;;;;;;0CAGrF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAM,eAAe,SAAS;wCAC9B,cAAc,SAAS,WAAW,iBAAiB;wCACnD,QAAQ;wCACR,WAAW,CAAC,gLAAgL,EAC1L,OAAO,QAAQ,GACX,gEACA,8CACJ;wCACF,aAAa,SAAS,WAAW,0CAA0C;wCAC3E,OAAO;wCACP,UAAU,CAAC;4CACT,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC1B,gBAAgB;wCAClB;;;;;;kDAEF,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAET,6BACC,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;iEAGvE,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;;8DACjE,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;8DACrE,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;4BAK5E,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA+C,OAAO,QAAQ;;;;;;4BAE5E,SAAS,YAAY,CAAC,OAAO,QAAQ,kBACpC,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;YAQvD,yBACC,6LAAC;gBAAI,WAAW,CAAC,uBAAuB,EACtC,QAAQ,QAAQ,CAAC,gBACb,sHACA,yGACJ;0BACC;;;;;;0BAKL,6LAAC;0BACC,cAAA,6LAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,wBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;4BACd,SAAS,WAAW,wBAAwB;;;;;;+BAG/C,SAAS,WAAW,mBAAmB;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,aAAa,SAAS,WAAW,WAAW;wBAC3D,WAAU;kCAET,SAAS,WAAW,qCAAqC;;;;;;oBAG3D,SAAS,0BACR,6LAAC;kCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;GApNgB;;QASkB,4IAAA,CAAA,uBAAoB;;;KATtC", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { ClientThemeToggle } from '@/components/client-theme-toggle'\nimport { AuthForm } from '@/components/auth-form'\n\nexport default function Login() {\n  const [isSignUp, setIsSignUp] = useState(false)\n\n\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6\">\n          <div className=\"flex justify-between items-center\">\n            <Link\n              href=\"/\"\n              className=\"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors\"\n            >\n              My Blog\n            </Link>\n            <ClientThemeToggle />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <div className=\"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-md w-full space-y-8\">\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-2xl sm:text-3xl font-bold text-foreground mb-2\">\n              {isSignUp ? 'Create your account' : 'Welcome back'}\n            </h2>\n            <p className=\"text-muted-foreground\">\n              {isSignUp ? 'Join our community today' : 'Sign in to your account'}\n            </p>\n            <p className=\"mt-4 text-sm text-muted-foreground\">\n              Or{' '}\n              <Link href=\"/\" className=\"font-medium text-primary hover:text-primary/80 transition-colors\">\n                return to home\n              </Link>\n            </p>\n          </div>\n          <div className=\"bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8\">\n            <AuthForm\n              mode={isSignUp ? 'signup' : 'signin'}\n              onModeChange={(mode) => setIsSignUp(mode === 'signup')}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAIzC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,kJAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;0BAMxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CACX,WAAW,wBAAwB;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CACV,WAAW,6BAA6B;;;;;;8CAE3C,6LAAC;oCAAE,WAAU;;wCAAqC;wCAC7C;sDACH,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAmE;;;;;;;;;;;;;;;;;;sCAKhG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,WAAQ;gCACP,MAAM,WAAW,WAAW;gCAC5B,cAAc,CAAC,OAAS,YAAY,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;GAtDwB;KAAA", "debugId": null}}]}