'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ClientThemeToggle } from '@/components/client-theme-toggle'
import { AuthForm } from '@/components/auth-form'

export default function Login() {
  const [isSignUp, setIsSignUp] = useState(false)



  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex justify-between items-center">
            <Link
              href="/"
              className="text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors"
            >
              My Blog
            </Link>
            <ClientThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-2">
              {isSignUp ? 'Create your account' : 'Welcome back'}
            </h2>
            <p className="text-muted-foreground">
              {isSignUp ? 'Join our community today' : 'Sign in to your account'}
            </p>
            <p className="mt-4 text-sm text-muted-foreground">
              Or{' '}
              <Link href="/" className="font-medium text-primary hover:text-primary/80 transition-colors">
                return to home
              </Link>
            </p>
          </div>
          <div className="bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8">
            <AuthForm
              mode={isSignUp ? 'signup' : 'signin'}
              onModeChange={(mode) => setIsSignUp(mode === 'signup')}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
